export default defineNuxtRouteMiddleware(async (to) => {
  if (process.server) return

  const { currentUser, authInitialized } = useFirebaseAuth()

  // 等待 Firebase 认证初始化完成
  await new Promise<void>((resolve) => {
    if (authInitialized.value) {
      resolve()
      return
    }

    // 如果还未初始化，等待最多 3 秒
    let timeoutId: NodeJS.Timeout
    const unwatch = watch(authInitialized, (initialized) => {
      if (initialized) {
        clearTimeout(timeoutId)
        unwatch()
        resolve()
      }
    })

    // 3秒超时保护
    timeoutId = setTimeout(() => {
      console.warn('Firebase auth initialization timeout')
      unwatch()
      resolve()
    }, 3000)
  })

  // 检查是否为 GitHub 页面
  const isGitHubPage = to.path.startsWith('/github')

  if (!currentUser.value) {
    if (isGitHubPage) {
      // GitHub 页面：允许加载但标记需要认证
      // 通过 useState 传递认证状态给页面
      const needsAuth = useState('needs-auth', () => false)
      needsAuth.value = true
      return // 不重定向，让页面处理认证
    } else {
      // 其他页面：保持原有重定向逻辑
      return navigateTo('/analysis')
    }
  } else {
    // 用户已登录，清除认证需求标记
    const needsAuth = useState('needs-auth', () => false)
    needsAuth.value = false
  }
})
